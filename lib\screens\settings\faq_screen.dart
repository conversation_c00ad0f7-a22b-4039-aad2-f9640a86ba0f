/// 바라 부스 매니저 - 자주 묻는 질문 페이지
///
/// 사용자들이 자주 묻는 질문들을 탭별로 구분하여 제공하는 페이지입니다.
/// - 기능 사용법
/// - 결제 및 환불
/// - 오류 및 문제 해결
/// - 고객 지원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월

import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';
import '../../widgets/app_bar_styles.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('자주 묻는 질문', style: AppBarStyles.of(context)),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 탭 바 (앱바와 분리된 스타일)
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primarySeed,
                unselectedLabelColor: Colors.grey.shade600,
                indicatorColor: AppColors.primarySeed,
                indicatorWeight: 3,
                indicatorSize: TabBarIndicatorSize.label,
                labelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                tabs: const [
                  Tab(text: '앱 기능'),
                  Tab(text: '결제/환불'),
                  Tab(text: '오류 해결'),
                  Tab(text: '고객 지원'),
                ],
              ),
            ),
            // 탭 뷰
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildFeatureTab(),
                  _buildPaymentTab(),
                  _buildTroubleshootingTab(),
                  _buildSupportTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 기능 사용법 탭
  Widget _buildFeatureTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('상품 관리', [
          _buildFAQItem(
            '상품을 등록하려면 어떻게 하나요?',
            '개별 상품 등록:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 각 카테고리에서 "+" 버튼 터치\n'
            '3. 상품명, 가격, 수량, 판매자 입력\n'
            '4. 필요시 상품 이미지 추가\n'
            '5. "저장" 버튼으로 완료\n\n'
            '팁: 상품명은 카테고리 내에서 중복될 수 없습니다.',
            icon: Icons.add_box,
          ),
          _buildFAQItem(
            '대량으로 상품을 등록하려면?',
            '대량 상품 등록:\n'
            '1. 재고관리 화면 우측 상단의 설정 버튼\n'
            '2. "대량 상품 등록" 선택\n'
            '3. 여러 상품을 한 번에 입력\n'
            '4. 일괄 입력 기능으로 가격/수량/판매자 한번에 설정\n'
            '5. "등록" 버튼으로 완료\n\n'
            '엑셀 대량등록:\n'
            '• 재고관리 > 설정 > "엑셀로 대량등록"\n'
            '• 엑셀 파일을 업로드하여 한번에 등록',
            icon: Icons.upload_file,
          ),
          _buildFAQItem(
            '상품 이미지는 어떻게 추가하나요?',
            '상품 등록/수정 화면에서:\n'
            '1. 이미지 영역 터치\n'
            '2. "갤러리에서 선택" 또는 "카메라로 촬영" 선택\n'
            '3. 이미지 선택 후 자동 저장\n\n'
            '지원 형식: JPG, PNG\n'
            '권장 크기: 500x500px 이하',
            icon: Icons.add_photo_alternate,
          ),
        ], sectionIcon: Icons.inventory_2),
        _buildFAQSection('판매 진행', [
          _buildFAQItem(
            '판매는 어떻게 진행하나요?',
            'POS 화면에서 판매 진행:\n'
            '1. 하단 중앙의 POS 버튼 터치\n'
            '2. 판매할 상품들을 터치하여 선택\n'
            '3. 우측 주문 패널에서 수량 조정 (+ / - 버튼)\n'
            '4. 결제 방법 선택 (현금/카드/기타)\n'
            '5. "결제" 버튼으로 판매 완료\n\n'
            '판매와 동시에 재고가 자동 차감됩니다.',
            icon: Icons.point_of_sale,
          ),
          _buildFAQItem(
            '판매 내역은 어디서 확인하나요?',
            '"기록 및 통계" 탭에서 확인 가능:\n'
            '• 판매 기록 탭: 일별/월별 판매 내역\n'
            '• 통계 탭: 상품별 판매 현황, 차트\n'
            '• 결제 방법별 통계\n'
            '• 판매자별 실적\n\n'
            '다양한 차트와 그래프로 시각화됩니다.',
            icon: Icons.analytics,
          ),
        ], sectionIcon: Icons.attach_money),
        _buildFAQSection('선입금 관리', [
          _buildFAQItem(
            '선입금이란 무엇인가요?',
            '행사 전에 미리 받는 예약 결제입니다:\n'
            '• 고객이 미리 상품을 예약하고 결제\n'
            '• 행사 당일 상품 수령\n'
            '• 재고 관리와 매출 예측에 도움\n\n'
            '특히 인기 상품의 사전 판매에 유용합니다.',
            icon: Icons.payment,
          ),
          _buildFAQItem(
            '선입금은 어떻게 등록하나요?',
            '1. "선입금" 탭 선택\n'
            '2. "+" 버튼으로 새 선입금 등록\n'
            '3. 구매자 정보 입력 (이름, 연락처)\n'
            '4. 상품과 수량 선택\n'
            '5. 수령 예정일 설정\n'
            '6. 결제 정보 입력 후 등록\n\n'
            '메모 기능으로 특별 요청사항도 기록 가능합니다.',
            icon: Icons.add_card,
          ),
          _buildFAQItem(
            '선입금 수령 상태는 어떻게 관리하나요?',
            '선입금 수령 관리:\n\n'
            '수령 완료 처리:\n'
            '• 선입금 목록에서 해당 항목의 "수령" 버튼 터치\n'
            '• 버튼이 회색으로 변경되어 수령 완료 표시\n\n'
            '수령 취소:\n'
            '• 수령 완료된 항목의 버튼을 다시 터치\n'
            '• 다시 초록색 "수령" 버튼으로 변경\n\n'
            '선입금 수정/삭제:\n'
            '• 선입금 항목을 터치하여 상세 정보 확인\n'
            '• "수정" 또는 "삭제" 버튼 사용',
            icon: Icons.check_circle,
          ),
        ], sectionIcon: Icons.credit_card),
        _buildFAQSection('통계 및 분석', [
          _buildFAQItem(
            '매출 통계는 어디서 보나요?',
            '"기록 및 통계" 탭에서 확인:\n\n'
            '매출 현황:\n'
            '• 일별/월별 매출 그래프\n'
            '• 결제 방법별 통계\n'
            '• 상품별 판매 순위\n\n'
            '상세 분석:\n'
            '• 시간대별 판매 패턴\n'
            '• 판매자별 실적\n'
            '• 수익률 분석\n\n'
            '프로 플랜에서는 더 상세한 분석 제공',
            icon: Icons.bar_chart,
          ),
          _buildFAQItem(
            '데이터를 엑셀로 내보낼 수 있나요?',
            '네, 다양한 형식으로 내보내기 가능:\n\n'
            '지원 형식:\n'
            '• Excel (.xlsx)\n'
            '• PDF 보고서\n'
            '• CSV 파일\n\n'
            '내보내기 데이터:\n'
            '• 판매 내역\n'
            '• 상품 목록\n'
            '• 선입금 현황\n'
            '• 매출 통계\n\n'
            '기록 및 통계 > 내보내기 메뉴에서 이용',
            icon: Icons.file_download,
          ),
        ], sectionIcon: Icons.analytics),
        _buildFAQSection('세트 할인', [
          _buildFAQItem(
            '세트 할인은 어떻게 설정하나요?',
            '세트 할인 등록:\n'
            '1. 재고관리 > 설정 > "세트 할인 관리"\n'
            '2. "+" 버튼으로 새 세트 생성\n'
            '3. 세트명과 할인 금액 입력\n'
            '4. 할인 조건 설정 (상품 조합 또는 총액 기준)\n'
            '5. 포함할 상품들 선택\n'
            '6. "저장" 버튼으로 완료\n\n'
            '예: "음료 3개 세트 1000원 할인"',
            icon: Icons.local_offer,
          ),
          _buildFAQItem(
            '세트 할인이 적용 안 돼요',
            '확인 사항:\n\n'
            '세트 활성화:\n'
            '• 세트 할인이 활성화 상태인지 확인\n'
            '• 세트 할인 목록에서 토글 버튼 확인\n\n'
            '조건 충족:\n'
            '• 세트에 포함된 모든 상품 선택했는지 확인\n'
            '• 최소 구매 조건 충족했는지 확인\n\n'
            '재고 확인:\n'
            '• 세트 상품들의 재고가 충분한지 확인',
            icon: Icons.error_outline,
          ),
        ], sectionIcon: Icons.discount),
        _buildFAQSection('📋 체크리스트', [
          _buildFAQItem(
            '체크리스트는 어떻게 사용하나요?',
            '행사 준비 체크리스트 관리:\n\n'
            '📱 접근 방법:\n'
            '• 홈 화면의 "준비물 체크리스트" 카드 터치\n'
            '• 또는 직접 체크리스트 화면으로 이동\n\n'
            '✅ 사용법:\n'
            '1. 체크리스트 항목을 터치하여 완료 표시\n'
            '2. 완료된 항목은 취소선 표시\n'
            '3. 우측 상단 편집 버튼으로 항목 추가/수정\n'
            '4. 변경사항은 자동으로 저장\n\n'
            '� 홈 화면에서 진행률을 바로 확인할 수 있습니다.',
          ),
        ]),
        _buildFAQSection('데이터 내보내기', [
          _buildFAQItem(
            '데이터를 엑셀이나 PDF로 내보낼 수 있나요?',
            '기록 및 통계 > 통계 탭에서 내보내기:\n\n'
            '지원 형식:\n'
            '• Excel (.xlsx) - 플러스 플랜 이상\n'
            '• PDF - 프로 플랜 전용\n\n'
            '내보내기 가능한 데이터:\n'
            '• 판매 내역 및 통계\n'
            '• 상품별 판매 현황\n'
            '• 매출 분석 보고서\n'
            '• 선입금 현황\n\n'
            '통계 탭 우측 상단의 다운로드 버튼을 사용하세요.',
            icon: Icons.download,
          ),
        ], sectionIcon: Icons.file_download),
        _buildFAQSection('🏷️ 카테고리 및 판매자 관리', [
          _buildFAQItem(
            '카테고리는 어떻게 관리하나요?',
            '상품 카테고리 관리:\n'
            '1. 재고관리 화면에서 카테고리 추가/수정\n'
            '2. 카테고리별로 상품 분류\n'
            '3. 카테고리 순서 변경 가능\n'
            '4. 색상으로 카테고리 구분\n\n'
            '💡 체계적인 상품 분류로 관리가 편해집니다.',
          ),
          _buildFAQItem(
            '재고 부족 알림은 어떻게 작동하나요?',
            '자동 재고 알림 시스템:\n\n'
            '🔴 재고 부족 표시:\n'
            '• 재고 5개 이하: 노란색 경고 표시\n'
            '• 재고 0개: 빨간색 품절 표시\n'
            '• 상품 목록에서 한눈에 확인 가능\n\n'
            '📱 알림 위치:\n'
            '• 재고관리 화면의 상품 카드\n'
            '• POS 화면의 상품 선택 시\n'
            '• 홈 화면의 재고 현황 카드\n\n'
            '⚡ 자동 업데이트:\n'
            '• 판매와 동시에 재고 자동 차감\n'
            '• 실시간으로 재고 상태 반영',
          ),
          _buildFAQItem(
            '판매자는 어떻게 관리하나요?',
            '판매자 정보 관리:\n\n'
            '👤 판매자 등록:\n'
            '• 상품 등록 시 판매자 선택/추가\n'
            '• 판매자명, 연락처 등 정보 입력\n'
            '• 수수료율 설정 가능\n\n'
            '📊 판매자별 분석:\n'
            '• 기록 및 통계 > 판매자별 실적\n'
            '• 매출, 수량, 수수료 계산\n'
            '• 정산 보고서 생성\n\n'
            '💰 정산 관리:\n'
            '• 판매자별 총 매출 확인\n'
            '• 수수료 자동 계산\n'
            '• 정산 내역 엑셀 내보내기\n\n'
            '💡 여러 판매자가 참여하는 행사에 필수 기능입니다.',
          ),
        ]),
        _buildFAQSection('🎯 수익 목표 관리', [
          _buildFAQItem(
            '수익 목표는 어떻게 설정하나요?',
            '홈 화면에서 수익 목표 설정:\n\n'
            '📊 설정 방법:\n'
            '1. 홈 화면의 "수익 목표" 카드 터치\n'
            '2. 목표 금액 입력\n'
            '3. 목표 기간 설정 (일별/월별)\n'
            '4. "저장" 버튼으로 완료\n\n'
            '📈 진행률 확인:\n'
            '• 홈 화면에서 실시간 진행률 확인\n'
            '• 원형 차트로 시각적 표시\n'
            '• 달성률 퍼센트 표시\n\n'
            '💡 목표 달성 시 축하 메시지가 표시됩니다.',
          ),
        ]),
        _buildFAQSection('🏠 홈 화면 기능', [
          _buildFAQItem(
            '홈 화면의 카드들은 무엇인가요?',
            '홈 화면 주요 기능 카드들:\n\n'
            '📊 오늘의 매출:\n'
            '• 당일 총 매출 금액 표시\n'
            '• 전일 대비 증감률 표시\n\n'
            '🎯 수익 목표:\n'
            '• 설정한 목표 대비 달성률\n'
            '• 원형 차트로 시각화\n\n'
            '📋 준비물 체크리스트:\n'
            '• 행사 준비 진행률 표시\n'
            '• 완료/전체 항목 수 표시\n\n'
            '📈 빠른 통계:\n'
            '• 주요 판매 지표 요약\n'
            '• 인기 상품 순위',
          ),
        ]),
        _buildFAQSection('👻 가상상품 관리', [
          _buildFAQItem(
            '가상상품이란 무엇인가요?',
            '선입금 전용 가상 상품:\n\n'
            '🎯 용도:\n'
            '• 선입금 등록 시에만 사용\n'
            '• 실제 재고가 없는 상품\n'
            '• 예약 판매용 상품\n\n'
            '⚙️ 관리 방법:\n'
            '1. 재고관리 > 설정 > "가상상품 관리"\n'
            '2. "+" 버튼으로 가상상품 추가\n'
            '3. 상품명과 가격만 설정\n'
            '4. 선입금 등록 시 선택 가능\n\n'
            '💡 실제 판매에서는 사용할 수 없습니다.',
          ),
        ]),
        _buildFAQSection('🎪 행사 관리', [
          _buildFAQItem(
            '행사는 어떻게 관리하나요?',
            '행사별 데이터 분리 관리:\n\n'
            '📅 행사 생성:\n'
            '• 새 행사 시작 시 행사 정보 입력\n'
            '• 행사명, 날짜, 장소 등 설정\n'
            '• 각 행사별로 독립적인 데이터 관리\n\n'
            '🔄 행사 전환:\n'
            '• 상단에서 현재 행사 선택\n'
            '• 드롭다운으로 다른 행사로 전환\n'
            '• 행사별 상품, 판매, 통계 분리\n\n'
            '📊 행사별 분석:\n'
            '• 각 행사의 독립적인 매출 분석\n'
            '• 행사 간 성과 비교\n'
            '• 행사별 보고서 생성',
          ),
        ]),
        _buildFAQSection('💾 데이터 백업/복원', [
          _buildFAQItem(
            '데이터 백업은 어떻게 하나요?',
            '플랜별 백업 방식:\n\n'
            '🎉 프로 플랜:\n'
            '• 실시간 클라우드 동기화\n'
            '• 자동 백업 (실시간 동기화 ON 시)\n'
            '• 여러 기기에서 동일한 데이터 접근\n\n'
            '🌟 플러스 플랜:\n'
            '• 로컬 백업만 지원\n'
            '• My 페이지 > 데이터 관리 > 백업\n'
            '• 수동 백업 파일 생성\n\n'
            '📋 무료 플랜:\n'
            '• 백업 기능 제한\n'
            '• 앱 삭제 시 데이터 손실 위험\n\n'
            '⚠️ 정기적인 백업을 권장합니다.',
          ),
        ]),
        _buildFAQSection('⚙️ 앱 설정', [
          _buildFAQItem(
            'My 페이지에는 어떤 기능들이 있나요?',
            'My 페이지 주요 기능들:\n\n'
            '👤 계정 정보:\n'
            '• 현재 구독 플랜 확인\n'
            '• 플랜 업그레이드/변경\n\n'
            '🔄 실시간 동기화 (프로 플랜):\n'
            '• 클라우드 동기화 ON/OFF\n'
            '• 마지막 동기화 시간 확인\n'
            '• 수동 동기화 버튼\n\n'
            '💾 데이터 관리:\n'
            '• 데이터 백업/복원\n'
            '• 캐시 정리\n'
            '• 앱 데이터 초기화\n\n'
            '📞 고객 지원:\n'
            '• 자주 묻는 질문\n'
            '• 문의하기\n'
            '• 앱 정보 및 버전',
          ),
        ]),
        _buildFAQSection('📈 통계 및 분석', [
          _buildFAQItem(
            '통계 화면에서는 어떤 정보를 볼 수 있나요?',
            '기록 및 통계 > 통계 탭의 주요 정보:\n\n'
            '📊 핵심 지표 (2x2 그리드):\n'
            '• 총 매출: 전체 판매 금액\n'
            '• 총 거래수: 판매 건수\n'
            '• 평균 거래액: 건당 평균 금액\n'
            '• 총 판매량: 판매된 상품 개수\n\n'
            '📈 차트 분석:\n'
            '• 일별/월별 매출 추이\n'
            '• 상품별 판매 순위\n'
            '• 시간대별 매출 분석\n'
            '• 결제 방법별 통계\n\n'
            '🏆 인기 상품 순위:\n'
            '• 판매량 기준 TOP 상품\n'
            '• 매출 기준 TOP 상품\n'
            '• 수익률 분석',
          ),
        ]),
      ],
    );
  }

  /// 결제/환불 탭
  Widget _buildPaymentTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('💳 구독 및 결제', [
          _buildFAQItem(
            '플랜별 차이점이 무엇인가요?',
            '📋 무료 플랜:\n'
            '• 행사 1개 제한\n'
            '• 상품 30개 제한\n'
            '• 로컬 저장만 가능\n\n'
            '🌟 플러스 플랜 (월 3,500원):\n'
            '• 무제한 행사 및 상품\n'
            '• 세트 할인 기능\n'
            '• 판매자별 관리\n'
            '• 엑셀 내보내기\n'
            '• 고급 통계 분석\n'
            '• 로컬 저장만 (클라우드 동기화 불가)\n\n'
            '🎉 프로 플랜 (월 4,900원):\n'
            '• 플러스 플랜의 모든 기능\n'
            '• 실시간 클라우드 동기화\n'
            '• PDF 내보내기\n'
            '• 실시간 백업 및 복원\n'
            '• 여러 기기에서 동시 사용 가능',
          ),
          _buildFAQItem(
            '결제는 언제 이루어지나요?',
            '구독 결제 일정:\n'
            '• 첫 결제: 구독 즉시\n'
            '• 정기 결제: 매월 구독일에 자동 결제\n'
            '• 결제 시간: 자정(00:00) 자동 처리\n\n'
            '💳 등록된 카드로 자동 결제되며,\n'
            '결제 실패 시 이메일로 안내드립니다.',
          ),
          _buildFAQItem(
            '플러스에서 프로로 업그레이드하면?',
            '업그레이드 시 차액만 결제됩니다:\n\n'
            '예시) 플러스 플랜 15일 사용 후 업그레이드:\n'
            '• 플러스 플랜 남은 15일: 1,750원\n'
            '• 프로 플랜 15일: 2,450원\n'
            '• 차액 결제: 700원\n\n'
            '✅ 다음 정기결제부터는 프로 플랜 전액(4,900원) 결제',
          ),
        ]),
        _buildFAQSection('🔄 구독 관리', [
          _buildFAQItem(
            '구독을 취소하려면?',
            'My 페이지 > 구독 관리에서:\n'
            '1. "구독 취소" 버튼 클릭\n'
            '2. 취소 확인 다이얼로그에서 "구독 취소" 선택\n\n'
            '⚠️ 중요사항:\n'
            '• 즉시 취소되지 않습니다\n'
            '• 다음 결제일까지 현재 플랜 유지\n'
            '• 다음 결제일 이후 무료 플랜으로 전환\n'
            '• 이미 결제된 금액은 환불되지 않습니다',
          ),
          _buildFAQItem(
            '환불은 어떻게 받나요?',
            '환불 절차:\n'
            '1. 먼저 구독 취소 진행 (My 페이지 > 구독 관리)\n'
            '2. 고객센터 이메일로 환불 요청\n'
            '3. 다음 정보 포함 필수:\n'
            '   - 등록된 이메일 주소\n'
            '   - 환불 사유\n'
            '   - 결제 영수증 (스크린샷)\n\n'
            '⚠️ 중요: 환불 진행 시 현재 활성화된 모든 구독 기능이 즉시 취소됩니다.\n'
            '⏰ 처리 기간: 영업일 기준 3-5일\n'
            '💳 환불 방법: 원결제 수단으로 환불',
          ),
        ]),
      ],
    );
  }

  /// 오류 해결 탭
  Widget _buildTroubleshootingTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('📱 앱 관련 문제', [
          _buildFAQItem(
            '앱이 느리거나 멈춰요',
            '해결 방법 (순서대로 시도):\n\n'
            '1️⃣ 앱 재시작:\n'
            '• 앱을 완전히 종료 후 재실행\n\n'
            '2️⃣ 캐시 정리:\n'
            '• My 페이지 > 설정 > 캐시 정리\n\n'
            '3️⃣ 저장 공간 확보:\n'
            '• 디바이스 저장 공간 10% 이상 확보\n\n'
            '4️⃣ 앱 업데이트:\n'
            '• 앱스토어/플레이스토어에서 최신 버전 확인\n\n'
            '5️⃣ 디바이스 재시작:\n'
            '• 스마트폰/태블릿 재부팅',
          ),
          _buildFAQItem(
            '데이터가 사라졌어요',
            '데이터 복구 방법:\n\n'
            '🎉 프로 플랜 사용자:\n'
            '• 실시간 동기화가 활성화된 경우 자동 복구\n'
            '• My 페이지 > 실시간 동기화 활성화\n'
            '• 몇 분 후 클라우드에서 데이터 자동 복원\n\n'
            '🌟 플러스/📋 무료 플랜 사용자:\n'
            '• 로컬 저장만 지원되므로 복구 방법 없음\n'
            '• 앱 삭제나 기기 변경 시 데이터 손실\n'
            '• 정기적인 수동 백업 권장\n\n'
            '⚠️ 로컬 데이터 손실 시 복구 불가능',
          ),
        ]),
        _buildFAQSection('🔄 동기화 문제', [
          _buildFAQItem(
            '실시간 동기화가 안 돼요',
            '실시간 동기화 문제 해결:\n\n'
            '1️⃣ 플랜 확인:\n'
            '• 프로 플랜에서만 사용 가능\n'
            '• My 페이지에서 현재 플랜 확인\n\n'
            '2️⃣ 동기화 활성화:\n'
            '• My 페이지 > "실시간 동기화" 토글 ON\n'
            '• 토글이 초록색이어야 활성화 상태\n\n'
            '3️⃣ 네트워크 확인:\n'
            '• 안정적인 Wi-Fi 또는 모바일 데이터 필요\n'
            '• 방화벽이나 보안 프로그램 확인\n\n'
            '4️⃣ 동기화 상태 확인:\n'
            '• My 페이지에서 "마지막 동기화" 시간 확인\n'
            '• 수동 동기화 버튼으로 강제 동기화\n\n'
            '5️⃣ 문제 지속 시:\n'
            '• 앱 완전 종료 후 재실행\n'
            '• 로그아웃 후 재로그인',
          ),
        ]),
      ],
    );
  }

  /// 고객 지원 탭
  Widget _buildSupportTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('📞 연락처 정보', [
          _buildContactItem(
            '이메일 문의',
            '<EMAIL>',
            '기술 지원 및 문의사항',
            Icons.email,
            () => _launchEmail(),
          ),
          _buildContactItem(
            '전화 문의',
            '1588-0000',
            '평일 9:00-18:00',
            Icons.phone,
            null,
          ),
        ]),
        _buildFAQSection('📋 문의 시 필요 정보', [
          _buildFAQItem(
            '문의할 때 어떤 정보가 필요한가요?',
            '빠른 해결을 위해 다음 정보를 포함해주세요:\n\n'
            '📱 기기 정보:\n'
            '• 기기 모델 (예: iPhone 14, Galaxy S23)\n'
            '• 운영체제 버전 (iOS 17.0, Android 13)\n\n'
            '📋 앱 정보:\n'
            '• 앱 버전 (My 페이지 하단에서 확인)\n'
            '• 구독 플랜 (무료/플러스/프로)\n\n'
            '🐛 문제 상황:\n'
            '• 언제 발생했는지\n'
            '• 어떤 작업 중이었는지\n'
            '• 오류 메시지 (있는 경우)\n'
            '• 스크린샷 (가능한 경우)',
          ),
        ]),
        _buildFAQSection('🔧 자가 진단', [
          _buildFAQItem(
            '문의 전 먼저 확인해보세요',
            '1️⃣ 앱 버전 확인:\n'
            '• 최신 버전인지 앱스토어에서 확인\n\n'
            '2️⃣ 네트워크 연결:\n'
            '• Wi-Fi 또는 모바일 데이터 정상 작동\n\n'
            '3️⃣ 저장 공간:\n'
            '• 디바이스 저장 공간 충분한지 확인\n\n'
            '4️⃣ 권한 설정:\n'
            '• 앱 권한이 모두 허용되어 있는지 확인\n\n'
            '5️⃣ 재시작:\n'
            '• 앱 재시작, 필요시 디바이스 재부팅',
          ),
        ]),
      ],
    );
  }

  /// FAQ 섹션 빌더
  Widget _buildFAQSection(String title, List<Widget> items, {IconData? sectionIcon}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            children: [
              if (sectionIcon != null) ...[
                Icon(sectionIcon, color: AppColors.primarySeed, size: 24),
                const SizedBox(width: 8),
              ],
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primarySeed,
                ),
              ),
            ],
          ),
        ),
        ...items,
        const SizedBox(height: 24),
      ],
    );
  }

  /// FAQ 아이템 빌더
  Widget _buildFAQItem(String question, String answer, {IconData? icon}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: icon != null ? Icon(icon, color: AppColors.primarySeed, size: 20) : null,
        title: Text(
          question,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              answer,
              style: const TextStyle(
                fontSize: 14,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 연락처 아이템 빌더
  Widget _buildContactItem(
    String title,
    String content,
    String subtitle,
    IconData icon,
    VoidCallback? onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: AppColors.primarySeed),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(content, style: const TextStyle(fontSize: 16)),
            Text(subtitle, style: const TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
        onTap: onTap,
        trailing: onTap != null ? const Icon(Icons.arrow_forward_ios, size: 16) : null,
      ),
    );
  }

  /// 이메일 앱 실행
  Future<void> _launchEmail() async {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('이메일 주소가 클립보드에 복사되었습니다: <EMAIL>'),
          backgroundColor: AppColors.primarySeed,
        ),
      );
    }
  }
}
